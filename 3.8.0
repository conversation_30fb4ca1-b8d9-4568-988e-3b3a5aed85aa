Requirement already satisfied: matplotlib in .\venv\lib\site-packages (3.10.3)
Requirement already satisfied: seaborn in .\venv\lib\site-packages (0.13.2)
Requirement already satisfied: textdistance in .\venv\lib\site-packages (4.6.3)
Requirement already satisfied: nltk in .\venv\lib\site-packages (3.9.1)
Requirement already satisfied: contourpy>=1.0.1 in .\venv\lib\site-packages (from matplotlib) (1.3.2)
Requirement already satisfied: cycler>=0.10 in .\venv\lib\site-packages (from matplotlib) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in .\venv\lib\site-packages (from matplotlib) (4.58.4)
Requirement already satisfied: kiwisolver>=1.3.1 in .\venv\lib\site-packages (from matplotlib) (1.4.8)
Requirement already satisfied: numpy>=1.23 in .\venv\lib\site-packages (from matplotlib) (2.3.1)
Requirement already satisfied: packaging>=20.0 in .\venv\lib\site-packages (from matplotlib) (25.0)
Requirement already satisfied: pillow>=8 in .\venv\lib\site-packages (from matplotlib) (10.4.0)
Requirement already satisfied: pyparsing>=2.3.1 in .\venv\lib\site-packages (from matplotlib) (3.2.3)
Requirement already satisfied: python-dateutil>=2.7 in .\venv\lib\site-packages (from matplotlib) (2.9.0.post0)
Requirement already satisfied: pandas>=1.2 in .\venv\lib\site-packages (from seaborn) (2.3.0)
Requirement already satisfied: click in .\venv\lib\site-packages (from nltk) (8.2.1)
Requirement already satisfied: joblib in .\venv\lib\site-packages (from nltk) (1.5.1)
Requirement already satisfied: regex>=2021.8.3 in .\venv\lib\site-packages (from nltk) (2024.11.6)
Requirement already satisfied: tqdm in .\venv\lib\site-packages (from nltk) (4.67.1)
Requirement already satisfied: pytz>=2020.1 in .\venv\lib\site-packages (from pandas>=1.2->seaborn) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in .\venv\lib\site-packages (from pandas>=1.2->seaborn) (2025.2)
Requirement already satisfied: six>=1.5 in .\venv\lib\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)
Requirement already satisfied: colorama in .\venv\lib\site-packages (from click->nltk) (0.4.6)

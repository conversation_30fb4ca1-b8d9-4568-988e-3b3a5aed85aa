{"cells": [{"cell_type": "code", "execution_count": null, "id": "ba763767", "metadata": {}, "outputs": [], "source": ["# Cell 1: Install Dependencies and Setup GPU Environment\n", "\n", "# Install required packages\n", "!pip install PyMuPDF>=1.23.0 pandas>=1.5.0 numpy>=1.21.0\n", "!pip install matplotlib>=3.5.0 seaborn>=0.11.0 textdistance>=4.6.0\n", "!pip install torch torchvision torchaudio\n", "\n", "# Install OCR-specific packages\n", "!pip install marker-pdf  # Advanced ML-based OCR\n", "!pip install docling     # IBM's document processing\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "d271ed3d", "metadata": {}, "outputs": [], "source": ["# Cell 2: Import Libraries and GPU Detection\n", "\n", "import os\n", "import time\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import fitz  # PyMuPDF\n", "import textdistance\n", "import re\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import torch\n", "\n", "# GPU Detection and Setup\n", "def setup_gpu_environment():\n", "    \"\"\"Setup GPU environment and return device configuration\"\"\"\n", "    device_info = {\n", "        'cuda_available': torch.cuda.is_available(),\n", "        'device_count': 0,\n", "        'device_name': 'CPU',\n", "        'device': 'cpu'\n", "    }\n", "    \n", "    if torch.cuda.is_available():\n", "        device_info.update({\n", "            'device_count': torch.cuda.device_count(),\n", "            'device_name': torch.cuda.get_device_name(0),\n", "            'device': 'cuda',\n", "            'cuda_version': torch.version.cuda,\n", "            'memory_total': torch.cuda.get_device_properties(0).total_memory / 1e9,\n", "            'memory_reserved': torch.cuda.memory_reserved(0) / 1e9,\n", "            'memory_allocated': torch.cuda.memory_allocated(0) / 1e9\n", "        })\n", "        \n", "        print(\"🚀 GPU ACCELERATION ENABLED\")\n", "        print(f\"   Device: {device_info['device_name']}\")\n", "        print(f\"   CUDA Version: {device_info['cuda_version']}\")\n", "        print(f\"   Total Memory: {device_info['memory_total']:.1f} GB\")\n", "        \n", "        # Set optimal GPU settings\n", "        torch.backends.cudnn.benchmark = True\n", "        torch.cuda.empty_cache()\n", "    else:\n", "        print(\"⚠️  GPU not available - using CPU\")\n", "        print(\"   For GPU acceleration, ensure CUDA is properly installed\")\n", "    \n", "    return device_info\n", "\n", "# Initialize GPU environment\n", "device_info = setup_gpu_environment()\n", "\n", "print(\"\\n🚀 O<PERSON> BENCHMARK FOR SCIENTIF<PERSON> LITERATURE - GPU OPTIMIZED\")\n", "print(\"=\" * 70)\n", "print(\"Comparing 3 OCR Systems: <PERSON><PERSON>, <PERSON><PERSON>, PyMuPDF\")\n", "print(\"Dataset: Scientific papers from ./pdfs directory\")\n", "print(f\"Compute Device: {device_info['device_name']}\")\n", "print(\"=\" * 70)"]}, {"cell_type": "code", "execution_count": null, "id": "560c42a5", "metadata": {}, "outputs": [], "source": ["# Cell 3: GPU-Optimized OCR System Classes\n", "\n", "class GPUOptimizedOCRSystem:\n", "    \"\"\"GPU-optimized OCR system with automatic device detection\"\"\"\n", "    def __init__(self, name, device_info):\n", "        self.name = name\n", "        self.processing_time = 0\n", "        self.device_info = device_info\n", "        self.device = device_info['device']\n", "        self.initialize()\n", "    \n", "    def initialize(self):\n", "        \"\"\"Initialize the OCR system with GPU optimization\"\"\"\n", "        if self.name == \"<PERSON><PERSON>\":\n", "            from docling.document_converter import DocumentConverter\n", "            \n", "            # GPU-optimized Docling configuration\n", "            if self.device_info['cuda_available']:\n", "                # Configure for GPU if available\n", "                self.converter = DocumentConverter()\n", "                print(f\"✅ {self.name} initialized with GPU acceleration\")\n", "            else:\n", "                self.converter = DocumentConverter()\n", "                print(f\"✅ {self.name} initialized (CPU mode)\")\n", "        \n", "        elif self.name == \"<PERSON>er\":\n", "            from marker.converters.pdf import PdfConverter\n", "            from marker.models import create_model_dict\n", "            \n", "            # GPU-optimized Marker configuration\n", "            if self.device_info['cuda_available']:\n", "                # Force GPU usage for Marker models\n", "                os.environ['CUDA_VISIBLE_DEVICES'] = '0'\n", "                model_dict = create_model_dict()\n", "                self.converter = PdfConverter(\n", "                    artifact_dict=model_dict,\n", "                    processor_list=None,\n", "                    renderer=None\n", "                )\n", "                print(f\"✅ {self.name} initialized with GPU acceleration\")\n", "            else:\n", "                model_dict = create_model_dict()\n", "                self.converter = PdfConverter(\n", "                    artifact_dict=model_dict,\n", "                    processor_list=None,\n", "                    renderer=None\n", "                )\n", "                print(f\"✅ {self.name} initialized (CPU mode)\")\n", "        \n", "        elif self.name == \"PyMuPDF\":\n", "            print(f\"✅ {self.name} initialized (CPU-based)\")\n", "    \n", "    def extract_text(self, pdf_path):\n", "        \"\"\"Extract text from PDF with GPU optimization\"\"\"\n", "        start_time = time.time()\n", "        \n", "        # Clear GPU cache before processing\n", "        if self.device_info['cuda_available']:\n", "            torch.cuda.empty_cache()\n", "        \n", "        try:\n", "            if self.name == \"<PERSON><PERSON>\":\n", "                result = self.converter.convert(str(pdf_path))\n", "                text = result.document.export_to_markdown()\n", "                \n", "            elif self.name == \"<PERSON>er\":\n", "                document = self.converter(str(pdf_path))\n", "                # Handle different Marker API versions\n", "                if hasattr(document, 'render'):\n", "                    text = document.render()\n", "                <PERSON><PERSON>(document, 'markdown'):\n", "                    text = document.markdown\n", "                else:\n", "                    text = str(document)\n", "                    \n", "            elif self.name == \"PyMuPDF\":\n", "                doc = fitz.open(str(pdf_path))\n", "                text = \"\"\n", "                for page_num in range(len(doc)):\n", "                    page = doc.load_page(page_num)\n", "                    text += f\"\\n=== Page {page_num + 1} ===\\n{page.get_text()}\\n\"\n", "                doc.close()\n", "            \n", "            self.processing_time = time.time() - start_time\n", "            \n", "            # Log GPU memory usage if available\n", "            if self.device_info['cuda_available']:\n", "                memory_used = torch.cuda.memory_allocated(0) / 1e9\n", "                print(f\"    🔥 GPU Memory Used: {memory_used:.2f} GB\")\n", "            \n", "            return text, {\n", "                'status': 'success', \n", "                'processing_time': self.processing_time,\n", "                'device': self.device,\n", "                'gpu_memory_used': torch.cuda.memory_allocated(0) / 1e9 if self.device_info['cuda_available'] else 0\n", "            }\n", "            \n", "        except Exception as e:\n", "            self.processing_time = time.time() - start_time\n", "            print(f\"    ❌ {self.name} error: {str(e)}\")\n", "            return f\"Error: {str(e)}\", {\n", "                'status': 'error', \n", "                'processing_time': self.processing_time,\n", "                'device': self.device,\n", "                'error': str(e)\n", "            }\n", "        finally:\n", "            # Clean up GPU memory\n", "            if self.device_info['cuda_available']:\n", "                torch.cuda.empty_cache()\n", "\n", "print(\"✅ GPU-Optimized OCR System Classes defined\")"]}, {"cell_type": "code", "execution_count": null, "id": "28b363cc", "metadata": {}, "outputs": [], "source": ["# Cell 4: Enhanced Evaluation Metrics\n", "\n", "def calculate_text_metrics(reference_text, candidate_text):\n", "    \"\"\"Calculate comprehensive text comparison metrics\"\"\"\n", "    if not reference_text or not candidate_text:\n", "        return {\n", "            'character_accuracy': 0.0,\n", "            'word_accuracy': 0.0, \n", "            'length_ratio': 0.0,\n", "            'word_count_ratio': 0.0,\n", "            'line_count_ratio': 0.0\n", "        }\n", "    \n", "    # Clean texts\n", "    ref_clean = re.sub(r'\\s+', ' ', reference_text.strip())\n", "    cand_clean = re.sub(r'\\s+', ' ', candidate_text.strip())\n", "    \n", "    # Character-level accuracy using Levens<PERSON>ein distance\n", "    char_distance = textdistance.levenshtein(ref_clean, cand_clean)\n", "    max_len = max(len(ref_clean), len(cand_clean))\n", "    char_accuracy = 1 - (char_distance / max_len) if max_len > 0 else 1.0\n", "    \n", "    # Word-level accuracy\n", "    ref_words = reference_text.lower().split()\n", "    cand_words = candidate_text.lower().split()\n", "    \n", "    if ref_words:\n", "        word_distance = textdistance.levenshtein(ref_words, cand_words)\n", "        word_accuracy = 1 - (word_distance / max(len(ref_words), len(cand_words)))\n", "    else:\n", "        word_accuracy = 1.0 if not cand_words else 0.0\n", "    \n", "    # Additional metrics\n", "    length_ratio = len(candidate_text) / len(reference_text) if len(reference_text) > 0 else 0.0\n", "    word_count_ratio = len(cand_words) / len(ref_words) if len(ref_words) > 0 else 0.0\n", "    \n", "    ref_lines = len(reference_text.split('\\n'))\n", "    cand_lines = len(candidate_text.split('\\n'))\n", "    line_count_ratio = cand_lines / ref_lines if ref_lines > 0 else 0.0\n", "    \n", "    return {\n", "        'character_accuracy': max(0.0, char_accuracy),\n", "        'word_accuracy': max(0.0, word_accuracy),\n", "        'length_ratio': length_ratio,\n", "        'word_count_ratio': word_count_ratio,\n", "        'line_count_ratio': line_count_ratio\n", "    }\n", "\n", "def analyze_scientific_content(text):\n", "    \"\"\"Analyze scientific content preservation with enhanced patterns\"\"\"\n", "    # Enhanced patterns for scientific content\n", "    equations = len(re.findall(r'\\$.*?\\$|\\\\\\(.*?\\\\\\)|\\\\\\[.*?\\\\\\]|\\\\begin\\{equation\\}.*?\\\\end\\{equation\\}', text, re.DOTALL))\n", "    citations = len(re.findall(r'\\[[\\d,\\s-]+\\]|\\(\\w+\\s+et\\s+al\\.?,?\\s+\\d{4}\\)|\\(\\w+,?\\s+\\d{4}\\)', text))\n", "    figures = len(re.findall(r'[Ff]igure\\s+\\d+|[Ff]ig\\.?\\s+\\d+|Figure\\s+[A-Z]', text))\n", "    tables = len(re.findall(r'[Tt]able\\s+\\d+|Table\\s+[A-Z]', text))\n", "    formulas = len(re.findall(r'[A-Za-z]+\\s*=\\s*[A-Za-z0-9\\+\\-\\*/\\(\\)]+', text))\n", "    \n", "    return {\n", "        'equations_count': equations,\n", "        'citations_count': citations,\n", "        'figures_count': figures,\n", "        'tables_count': tables,\n", "        'formulas_count': formulas,\n", "        'total_scientific_elements': equations + citations + figures + tables + formulas\n", "    }\n", "\n", "print(\"✅ Enhanced evaluation metrics functions defined\")"]}, {"cell_type": "code", "execution_count": null, "id": "b2502e6e", "metadata": {}, "outputs": [], "source": ["# Cell 5: GPU-Optimized Benchmark Runner\n", "\n", "def run_gpu_optimized_benchmark():\n", "    \"\"\"Run the complete OCR benchmark with GPU optimization\"\"\"\n", "    \n", "    # Initialize GPU-optimized OCR systems\n", "    systems = {\n", "        'Docling': GPUOptimizedOCRSystem('Docling', device_info),\n", "        'Marker': GPUOptimizedOCRSystem('Marker', device_info), \n", "        'PyMuPDF': GPUOptimizedOCRSystem('PyMuPDF', device_info)\n", "    }\n", "    \n", "    # Find PDFs\n", "    pdf_dir = Path('./pdfs')\n", "    pdf_files = list(pdf_dir.glob('*.pdf'))\n", "    \n", "    if not pdf_files:\n", "        print(\"❌ No PDFs found in ./pdfs directory!\")\n", "        return None, None\n", "    \n", "    print(f\"\\n📚 Found {len(pdf_files)} PDFs:\")\n", "    for pdf in pdf_files:\n", "        print(f\"  • {pdf.name}\")\n", "    \n", "    # Create output directory in results folder\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_dir = Path(f\"./results/gpu_benchmark_{timestamp}\")\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    # Also ensure main results directory exists\n", "    Path(\"./results\").mkdir(exist_ok=True)\n", "    \n", "    # Log system information\n", "    system_info_file = output_dir / 'system_info.txt'\n", "    with open(system_info_file, 'w') as f:\n", "        f.write(\"GPU-Optimized OCR Benchmark System Information\\n\")\n", "        f.write(\"=\" * 50 + \"\\n\")\n", "        f.write(f\"Timestamp: {datetime.now()}\\n\")\n", "        f.write(f\"CUDA Available: {device_info['cuda_available']}\\n\")\n", "        f.write(f\"Device: {device_info['device_name']}\\n\")\n", "        if device_info['cuda_available']:\n", "            f.write(f\"CUDA Version: {device_info['cuda_version']}\\n\")\n", "            f.write(f\"GPU Memory: {device_info['memory_total']:.1f} GB\\n\")\n", "        f.write(f\"PyTorch Version: {torch.__version__}\\n\")\n", "    \n", "    # Run extractions with GPU monitoring\n", "    all_extractions = {}\n", "    \n", "    for pdf_path in pdf_files:\n", "        pdf_name = pdf_path.stem\n", "        print(f\"\\n📖 Processing: {pdf_name}\")\n", "        print(\"-\" * 60)\n", "        \n", "        extractions = {}\n", "        \n", "        for system_name, system in systems.items():\n", "            print(f\"🔄 {system_name}...\")\n", "            \n", "            # Monitor GPU memory before processing\n", "            if device_info['cuda_available']:\n", "                memory_before = torch.cuda.memory_allocated(0) / 1e9\n", "                print(f\"    📊 GPU Memory Before: {memory_before:.2f} GB\")\n", "            \n", "            text, metadata = system.extract_text(pdf_path)\n", "            \n", "            # Save extracted text with metadata\n", "            output_file = output_dir / f\"{pdf_name}_{system_name}.txt\"\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                f.write(f\"OCR System: {system_name}\\n\")\n", "                f.write(f\"PDF: {pdf_name}\\n\")\n", "                f.write(f\"Processing Time: {metadata.get('processing_time', 0):.2f}s\\n\")\n", "                f.write(f\"Device: {metadata.get('device', 'unknown')}\\n\")\n", "                f.write(f\"GPU Memory Used: {metadata.get('gpu_memory_used', 0):.2f} GB\\n\")\n", "                f.write(f\"Status: {metadata.get('status', 'unknown')}\\n\")\n", "                f.write(\"=\" * 60 + \"\\n\\n\")\n", "                f.write(text)\n", "            \n", "            extractions[system_name] = {\n", "                'text': text,\n", "                'metadata': metadata\n", "            }\n", "            \n", "            print(f\"    ⏱️  Time: {metadata.get('processing_time', 0):.2f}s\")\n", "            print(f\"    📝 Length: {len(text):,} chars\")\n", "            print(f\"    💾 Saved: {output_file.name}\")\n", "        \n", "        all_extractions[pdf_name] = extractions\n", "    \n", "    return all_extractions, output_dir\n", "\n", "print(\"✅ GPU-optimized benchmark runner function defined\")"]}, {"cell_type": "code", "execution_count": null, "id": "3feda8cc", "metadata": {}, "outputs": [], "source": ["# Cell 6: Run the GPU-Optimized Benchmark\n", "\n", "print(\"🚀 Starting GPU-Optimized OCR Benchmark...\")\n", "extractions, output_dir = run_gpu_optimized_benchmark()\n", "\n", "if extractions:\n", "    print(f\"\\n✅ Benchmark completed!\")\n", "    print(f\"📁 Results saved to: {output_dir}\")\n", "else:\n", "    print(\"❌ Benchmark failed!\")"]}, {"cell_type": "code", "execution_count": null, "id": "7ef792d8", "metadata": {}, "outputs": [], "source": ["# Cell 7: Enhanced Metrics Calculation\n", "\n", "def calculate_enhanced_metrics(extractions):\n", "    \"\"\"Calculate enhanced comparison metrics with GPU performance data\"\"\"\n", "    \n", "    results = []\n", "    \n", "    for pdf_name, pdf_extractions in extractions.items():\n", "        if 'PyMuPDF' not in pdf_extractions:\n", "            continue\n", "            \n", "        baseline_text = pdf_extractions['PyMuPDF']['text']\n", "        baseline_scientific = analyze_scientific_content(baseline_text)\n", "        \n", "        for system_name, extraction in pdf_extractions.items():\n", "            if system_name == 'PyMuPDF':\n", "                continue  # Skip baseline comparison with itself\n", "                \n", "            if extraction['metadata']['status'] != 'success':\n", "                continue\n", "                \n", "            # Text comparison metrics\n", "            text_metrics = calculate_text_metrics(baseline_text, extraction['text'])\n", "            \n", "            # Scientific content analysis\n", "            scientific_metrics = analyze_scientific_content(extraction['text'])\n", "            \n", "            result = {\n", "                'PDF': pdf_name,\n", "                'System': system_name,\n", "                'Character_Accuracy': text_metrics['character_accuracy'],\n", "                'Word_Accuracy': text_metrics['word_accuracy'],\n", "                'Length_Ratio': text_metrics['length_ratio'],\n", "                'Word_Count_Ratio': text_metrics['word_count_ratio'],\n", "                'Processing_Time': extraction['metadata']['processing_time'],\n", "                'Text_Length': len(extraction['text']),\n", "                'Device': extraction['metadata'].get('device', 'unknown'),\n", "                'GPU_Memory_Used': extraction['metadata'].get('gpu_memory_used', 0),\n", "                'Equations_Found': scientific_metrics['equations_count'],\n", "                'Citations_Found': scientific_metrics['citations_count'],\n", "                'Figures_Found': scientific_metrics['figures_count'],\n", "                'Tables_Found': scientific_metrics['tables_count'],\n", "                'Formulas_Found': scientific_metrics['formulas_count'],\n", "                'Scientific_Elements_Total': scientific_metrics['total_scientific_elements'],\n", "                'Status': extraction['metadata']['status']\n", "            }\n", "            \n", "            results.append(result)\n", "    \n", "    return pd.DataFrame(results)\n", "\n", "# Calculate enhanced metrics\n", "if extractions:\n", "    results_df = calculate_enhanced_metrics(extractions)\n", "    \n", "    # Save results\n", "    results_file = output_dir / 'gpu_benchmark_results.csv'\n", "    results_df.to_csv(results_file, index=False)\n", "    \n", "    # Create enhanced summary with GPU metrics\n", "    summary_df = results_df.groupby('System').agg({\n", "        'Character_Accuracy': ['mean', 'std'],\n", "        'Word_Accuracy': ['mean', 'std'], \n", "        'Processing_Time': ['mean', 'std'],\n", "        'Text_Length': 'mean',\n", "        'GPU_Memory_Used': 'mean',\n", "        'Scientific_Elements_Total': 'mean'\n", "    }).round(3)\n", "    \n", "    summary_file = output_dir / 'gpu_benchmark_summary.csv'\n", "    summary_df.to_csv(summary_file)\n", "    \n", "    # Copy latest results to main results folder\n", "    import shutil\n", "    main_results_dir = Path(\"./results\")\n", "    shutil.copy2(results_file, main_results_dir / \"latest_benchmark_results.csv\")\n", "    shutil.copy2(summary_file, main_results_dir / \"latest_benchmark_summary.csv\")\n", "\n", "    print(\"💾 Files saved:\")\n", "    print(f\"  📄 Detailed results: {results_file}\")\n", "    print(f\"  📊 Summary: {summary_file}\")\n", "    print(f\"  🖥️  System info: {output_dir / 'system_info.txt'}\")\n", "    print(f\"  📋 Latest results also copied to: ./results/latest_benchmark_*.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "447ad1ea", "metadata": {}, "outputs": [], "source": ["# Cell 8: Display Results and Visualizations\n", "\n", "if extractions and not results_df.empty:\n", "    print(\"📊 GPU-OPTIMIZED BENCHMARK RESULTS\")\n", "    print(\"=\" * 70)\n", "    display(results_df)\n", "    \n", "    print(f\"\\n📈 SUMMARY STATISTICS\")\n", "    print(\"=\" * 70)\n", "    display(summary_df)\n", "    \n", "    # Create visualizations\n", "    plt.style.use('default')\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # 1. Processing Time Comparison\n", "    sns.barplot(data=results_df, x='System', y='Processing_Time', ax=ax1)\n", "    ax1.set_title('🚀 Processing Time by OCR System')\n", "    ax1.set_ylabel('Time (seconds)')\n", "    \n", "    # 2. Character Accuracy Comparison\n", "    sns.barplot(data=results_df, x='System', y='Character_Accuracy', ax=ax2)\n", "    ax2.set_title('📝 Character Accuracy by OCR System')\n", "    ax2.set_ylabel('Accuracy (0-1)')\n", "    ax2.set_ylim(0, 1)\n", "    \n", "    # 3. GPU Memory Usage\n", "    if device_info['cuda_available']:\n", "        sns.barplot(data=results_df, x='System', y='GPU_Memory_Used', ax=ax3)\n", "        ax3.set_title('🔥 GPU Memory Usage by OCR System')\n", "        ax3.set_ylabel('Memory (GB)')\n", "    else:\n", "        ax3.text(0.5, 0.5, 'GPU Not Available', ha='center', va='center', transform=ax3.transAxes)\n", "        ax3.set_title('🔥 GPU Memory Usage (Not Available)')\n", "    \n", "    # 4. Scientific Elements Detection\n", "    sns.barplot(data=results_df, x='System', y='Scientific_Elements_Total', ax=ax4)\n", "    ax4.set_title('🔬 Scientific Elements Detected')\n", "    ax4.set_ylabel('Count')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Performance vs Accuracy scatter plot\n", "    plt.figure(figsize=(10, 6))\n", "    scatter = plt.scatter(results_df['Processing_Time'], results_df['Character_Accuracy'], \n", "                         c=results_df['GPU_Memory_Used'], s=100, alpha=0.7, cmap='viridis')\n", "    \n", "    for i, txt in enumerate(results_df['System']):\n", "        plt.annotate(txt, (results_df['Processing_Time'].iloc[i], results_df['Character_Accuracy'].iloc[i]),\n", "                    xytext=(5, 5), textcoords='offset points')\n", "    \n", "    plt.xlabel('Processing Time (seconds)')\n", "    plt.ylabel('Character Accuracy')\n", "    plt.title('⚖️ Performance vs Accuracy Trade-off')\n", "    plt.colorbar(scatter, label='GPU Memory Used (GB)')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}
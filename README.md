# OCR Benchmarking System for Scientific Literature

**GSoC 2025 Project: Enhanced AI OCR Extraction Pipeline for Scientific Literature**

This repository contains a comprehensive benchmarking framework that evaluates and compares OCR systems on scientific literature. The project successfully benchmarks **3 OCR systems** (<PERSON><PERSON>, <PERSON><PERSON>, PyMuPDF) with **Mark<PERSON> achieving 82.6% accuracy** as the best performer.

## 🎯 Project Status: ✅ COMPLETE

**Deliverables Completed:**
- ✅ **3 OCR systems benchmarked** (Mark<PERSON> + 2 others as requested)
- ✅ **Google Colab Notebook** with findings and analysis
- ✅ **Comprehensive metrics** for scientific document processing
- ✅ **Processing time analysis** and performance comparison
- ✅ **Real scientific literature** used as test dataset

## 📊 Key Results

| OCR System | Character Accuracy | Processing Time | Best For |
|------------|-------------------|----------------|----------|
| **Marker** ⭐ | **82.6%** | 39s avg | **Best Overall Performance** |
| **Docling** | 81.3% | 91s avg | Scientific Content Analysis |
| **PyMuPDF** | Baseline | <1s | Speed Baseline |

## 🔍 Evaluation Metrics

The benchmarking system evaluates OCR systems across multiple dimensions:

- **Text Extraction Accuracy**: Character, word, and line-level accuracy
- **Structure Preservation**: Headers, paragraphs, equations, tables, figures  
- **Processing Performance**: Speed, efficiency, resource usage
- **Scientific Content Handling**: Formulas, notation, references, citations

## 🔍 OCR Systems Benchmarked

### **Successfully Tested ✅**
- **Marker** ⭐ - Priority system (82.6% accuracy, 39s avg processing)
- **Docling** - IBM's document AI (81.3% accuracy, 91s avg processing)  
- **PyMuPDF** - Direct text extraction baseline (<1s processing)

**Result**: Marker demonstrates the best overall performance for scientific literature OCR, balancing high accuracy with reasonable processing speed.

## 📂 Repository Structure

```
papers-ocr-benchmarks/
├── 📓 OCR_Benchmark_Scientific_Literature.ipynb  # 🎯 Google Colab Notebook (MAIN DELIVERABLE)
├── 📄 README.md                                  # Project documentation
├── 📋 requirements.txt                           # Python dependencies
│
├── 📁 scripts/                                   # Benchmark scripts
│   ├── ocr_benchmark_gpu_optimized.py           # Main benchmark script
│   └── setup_gpu_environment.py                 # Environment setup
│
├── 📁 pdfs/                                      # Test dataset (3 scientific papers)
│   ├── 2014-Combining_organophosphate_treated_wall_linings...pdf
│   ├── Allossogbe_et_al_2017_Mal_J.pdf
│   └── Somboon_et_al_1995_Trans_RSTMH.pdf
│
├── 📁 results/                                   # Benchmark results
│   ├── benchmark_results.csv                    # Detailed metrics
│   ├── benchmark_summary.csv                    # Summary statistics
│   ├── benchmark_visualization.png              # Performance charts
│   └── [individual_ocr_outputs].txt             # Raw OCR extractions
│
├── 📁 docs/                                      # Project documentation
│   └── Enhanced AI OCR Extraction Pipeline for Scientific Literature.md
│
└── 📁 output/                                    # Generated results directory
```

## 🚀 Quick Start

### **For GSoC Review (Recommended)**
1. **View Results**: Open `OCR_Benchmark_Scientific_Literature.ipynb` in Google Colab
2. **Check Metrics**: Review `results/benchmark_summary.csv`
3. **Inspect Outputs**: Examine individual OCR outputs in `results/`

### **To Run Benchmark Locally**
```bash
# 1. Setup environment
python scripts/setup_gpu_environment.py

# 2. Run benchmark
python scripts/ocr_benchmark_gpu_optimized.py

# 3. Check results in output/ directory
```

## 📊 Key Files

### **Main Deliverables**
- **`OCR_Benchmark_Scientific_Literature.ipynb`** - Google Colab notebook with complete analysis
- **`results/benchmark_summary.csv`** - Summary of all benchmark results
- **`results/benchmark_results.csv`** - Detailed per-document metrics

### **Scripts**
- **`scripts/ocr_benchmark_gpu_optimized.py`** - Main benchmark script with GPU optimization
- **`scripts/setup_gpu_environment.py`** - Environment setup and dependency checking

### **Data**
- **`pdfs/`** - Test dataset of 3 scientific papers
- **`results/`** - Complete benchmark results and individual OCR outputs

## 📈 Benchmark Results Summary

### **Performance Metrics**
- **Best Accuracy**: Marker (82.6% character accuracy)
- **Fastest Processing**: PyMuPDF (<1s per document)
- **Best Balance**: Marker (high accuracy + reasonable speed)

### **Scientific Content Analysis**
- **Citations Detected**: All systems successfully identify reference citations
- **Figures/Tables**: Good preservation of figure and table references
- **Mathematical Content**: Basic formula detection implemented

### **Processing Time Analysis**
- **Marker**: 39s average (best AI-based performance)
- **Docling**: 91s average (thorough but slower)
- **PyMuPDF**: <1s (direct text extraction baseline)

## 🔧 Technical Details

### **Dependencies**
```bash
pip install -r requirements.txt
```

Key packages:
- `marker-pdf` - Marker OCR system
- `docling` - IBM Docling system
- `PyMuPDF` - PDF text extraction
- `pandas`, `numpy` - Data analysis
- `torch` - GPU acceleration (optional)

### **System Requirements**
- Python 3.8+
- 8GB+ RAM recommended
- GPU optional (automatic CPU fallback)
- 5GB+ storage for results

